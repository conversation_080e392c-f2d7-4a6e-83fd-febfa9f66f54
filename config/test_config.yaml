# Claude Request Forwarder 测试配置
server:
  host: "127.0.0.1"
  port: 8087

strategy:
  type: "priority"

retry:
  max_attempts: 2
  base_delay: "1s"
  max_delay: "10s"
  multiplier: 2.0

health:
  check_interval: "30s"
  timeout: "5s"
  health_path: "/v1/models"

# 测试日志文件配置
logging:
  level: "info"
  format: "text"
  file_enabled: true                     # 启用文件日志
  file_path: "logs/test_app.log"         # 测试日志文件路径
  max_file_size: "1MB"                   # 测试用的小文件大小
  max_files: 3                           # 测试用的少文件数量
  compress_rotated: true                 # 启用压缩
  disable_response_limit: true           # 启用时取消响应限制

streaming:
  heartbeat_interval: "30s"
  read_timeout: "10s"
  max_idle_time: "120s"

global_timeout: "300s"

auth:
  enabled: false

tui:
  enabled: false                         # 测试时禁用TUI
  update_interval: "1s"

proxy:
  enabled: false

endpoints:
  - name: "test"
    url: "https://httpbin.org"           # 使用httpbin进行测试
    priority: 1
    timeout: "10s"
# Claude Request Forwarder Configuration
# 完整配置示例，展示所有可用的配置选项

# 服务器配置
server:
  host: "0.0.0.0"      # Docker环境中监听所有接口，默认: localhost
  port: 8087             # 监听端口，默认: 8080

# 路由策略配置(适用于组内)
strategy:
  type: "fastest"                  # 路由策略: "priority" (优先级) 或 "fastest" (最快响应)
  fast_test_enabled: true          # 启用快速测试 (仅在 fastest 策略下生效)
  fast_test_cache_ttl: "30s"       # 快速测试结果缓存时间，默认: 3s
  fast_test_timeout: "5s"          # 快速测试超时时间，默认: 1s  
  fast_test_path: "/v1/models"     # 快速测试路径，默认使用健康检查路径

# 重试配置
retry:
  max_attempts: 3        # 最大重试次数，默认: 3
  base_delay: "1s"       # 基础延迟时间，默认: 1s
  max_delay: "30s"       # 最大延迟时间，默认: 30s
  multiplier: 2.0        # 延迟倍数，默认: 2.0

# 健康检查配置
health:
  check_interval: "30s"  # 健康检查间隔，默认: 30s
  timeout: "5s"          # 健康检查超时，默认: 5s
  health_path: "/v1/models"  # 健康检查路径，默认: /v1/models

# 日志配置
logging:
  level: "info"          # 日志级别: debug, info, warn, error，默认: info
  format: "json"         # 日志格式: "json" 或 "text"，默认: text
  
  # 文件日志配置 (可选)
  file_enabled: false            # 是否启用文件日志，默认: false
  file_path: "logs/app.log"      # 日志文件路径，默认: logs/app.log
  max_file_size: "100MB"         # 单个日志文件最大大小，支持: KB, MB, GB，默认: 100MB
  max_files: 10                  # 最多保留的轮转文件数量，默认: 10
  compress_rotated: true         # 是否压缩轮转的旧日志文件，默认: false
  disable_response_limit: true   # 启用文件日志时是否取消响应内容输出限制，默认: false

# 流式传输配置
streaming:
  heartbeat_interval: "30s"  # 心跳间隔，默认: 30s
  read_timeout: "10s"         # 读取超时，默认: 1s
  max_idle_time: "120s"      # 最大空闲时间，默认: 120s

# 组管理配置
group:
  cooldown: "600s"                      # 组失败后的冷却时间，默认: 600s
  auto_switch_between_groups: true      # 组间自动切换：true=自动切换到其他组，false=需要手动切换，默认: true

# 请求挂起配置
request_suspend:
  enabled: false              # 是否启用请求挂起功能，默认: false
  timeout: "300s"             # 挂起请求的超时时间，默认: 300s (5分钟)
  max_suspended_requests: 100 # 最大挂起请求数量，默认: 100

# 全局超时配置
global_timeout: "300s"       # 非流式请求的全局默认超时时间，默认: 300s (5分钟)

# 鉴权配置 (可选)
auth:
  enabled: false             # 是否启用鉴权，默认: false (不鉴权)
  # token: "your-bearer-token"  # Bearer Token，启用鉴权时必须设置

# TUI界面配置,如果部署在服务器上建议设置为 false
tui:
  enabled: false               # Docker环境中禁用TUI界面，默认: true
  update_interval: "1s"       # TUI刷新间隔，默认: 1s
  save_priority_edits: false  # 是否在TUI中保存优先级编辑到配置文件，默认: false（当前情况下保存配置文件可能会自动格式化配置文件）

# Web界面配置
web:
  enabled: true              # Docker环境中启用Web界面，默认: false
  host: "0.0.0.0"          # Web界面监听所有接口，默认: localhost
  port: 8088                 # Web界面端口，默认: 8088

# 使用跟踪配置
# =================================================================
# 📊 使用情况追踪系统 (Usage Tracking)
# =================================================================
# 🪟 Windows兼容性保证: v1.0.2+ 完美支持Windows平台
# 使用纯Go SQLite驱动，无需C编译器，无CGO依赖
# 支持所有平台：Windows、Linux、macOS (Intel & Apple Silicon)
# =================================================================
usage_tracking:
  enabled: false                         # 是否启用使用跟踪，默认: false
  database_path: "data/usage.db"         # SQLite数据库文件路径，默认: data/usage.db
  
  # 💡 数据库说明:
  # - 使用纯Go SQLite驱动 (modernc.org/sqlite)，无CGO依赖
  # - 自动创建数据库文件和表结构 (首次运行时)
  # - 数据库文件可以直接复制用于备份和迁移
  # - 支持WAL模式，提供更好的并发性能
  
  # 异步写入配置 - 本地使用优化
  buffer_size: 300                      # 事件缓冲区大小，默认: 1000 (本地使用减小)
  batch_size: 15                        # 批量写入大小，默认: 100 (本地使用减小，快速持久化)
  flush_interval: "8s"                  # 强制刷新间隔，默认: 30s (本地使用加快刷新)
  max_retry: 3                           # 写入失败最大重试次数，默认: 3
  
  # 📈 性能特点:
  # - 完全异步处理，不影响请求转发性能
  # - 批量写入，减少数据库I/O开销
  # - 自动故障恢复，数据完整性保障
  # - 内存缓冲，平滑处理突发请求
  
  # 数据保留策略
  retention_days: 0                     # 数据保留天数 (0=永久保留)，默认: 90
  cleanup_interval: "24h"                # 清理任务执行间隔，默认: 24h
  
  # 📊 数据统计功能:
  # - Token使用量统计 (输入/输出/缓存创建/缓存读取)
  # - 成本分析 (基于模型定价自动计算)
  # - 端点性能监控 (响应时间、成功率)
  # - 请求生命周期追踪 (完整的请求流程记录)
  # - Web界面实时查看，支持CSV/JSON导出
  
  # 模型定价配置 (USD per 1M tokens) - 2025年最新官方定价
  model_pricing:
    "claude-sonnet-4-20250514":
      input: 3.00
      output: 15.00
      cache_creation: 3.75      # 1.25x input (5分钟缓存)
      cache_read: 0.30          # 0.1x input (缓存读取)
    "claude-3-5-haiku-20241022":
      input: 0.80
      output: 4.00
      cache_creation: 1.00      # 1.25x input
      cache_read: 0.08          # 0.1x input
    "claude-3-5-sonnet-20241022":  
      input: 3.00
      output: 15.00
      cache_creation: 3.75      # 1.25x input
      cache_read: 0.30          # 0.1x input
    "claude-opus-4":
      input: 15.00
      output: 75.00
      cache_creation: 18.75     # 1.25x input
      cache_read: 1.50          # 0.1x input
    "claude-opus-4.1":
      input: 15.00
      output: 75.00
      cache_creation: 18.75     # 1.25x input
      cache_read: 1.50          # 0.1x input
    # 兼容旧版本模型
    "claude-3-haiku-20240307":
      input: 0.25
      output: 1.25
      cache_creation: 0.31      # 1.25x input
      cache_read: 0.025         # 0.1x input
    "claude-3-sonnet-20240229":  
      input: 3.00
      output: 15.00
      cache_creation: 3.75      # 1.25x input
      cache_read: 0.30          # 0.1x input
    "claude-3-opus-20240229":
      input: 15.00
      output: 75.00
      cache_creation: 18.75     # 1.25x input
      cache_read: 1.50          # 0.1x input
    
  # 默认定价 (当模型不在配置中时使用)
  default_pricing:
    input: 3.00
    output: 15.00
    cache_creation: 3.75      # 1.25x input
    cache_read: 0.30          # 0.1x input

# 代理配置 (可选)
proxy:
  enabled: false              # 是否启用代理
  type: "http"               # 代理类型: "http", "https", 或 "socks5"
  
  # 方式一: 完整代理 URL
  # url: "http://proxy.example.com:8080"
  # url: "https://proxy.example.com:8080"
  # url: "socks5://proxy.example.com:1080"
  
  # 方式二: 分别指定主机和端点
  host: "proxy.example.com"   # 代理主机
  port: 8080                  # 代理端口
  
  # 可选的认证信息
  # username: "proxy_user"    # 代理用户名
  # password: "proxy_pass"    # 代理密码

# 端点配置
# ==================== 组密钥配置说明 ====================
# 每个组的第一个端点应该定义该组使用的 token 和 api-key
# 组内其他端点如果没有定义 token/api-key，会自动使用组内第一个端点的密钥
# 如果某个端点需要使用不同的密钥，可以显式指定 token/api-key 来覆盖组默认值
# ========================================================

endpoints:
  # ============ 主要组 (main) ============
  # 组定义端点 - 定义整个 main 组使用的密钥
  - name: "primary"
    url: "https://api.openai.com"
    group: "main"                          # 组名
    group-priority: 1                      # 组优先级 (数字越小优先级越高)
    priority: 1                            # 组内优先级 (数字越小优先级越高)
    timeout: "300s"
    token: "sk-your-openai-api-key"        # 🔑 此密钥会被同组其他端点共享
    api-key: "your-api-key-value"          # 🔑 此API密钥会被同组其他端点共享
    headers:
      User-Agent: "Claude-Request-Forwarder/1.0"
      X-Custom-Header: "custom-value"

  # 主要组备用端点 - 自动使用 main 组的密钥
  - name: "primary_backup"
    url: "https://api.anthropic.com"
    priority: 2                            # 组内优先级 2
    timeout: "300s"
    # 🔄 自动继承: group: "main", group-priority: 1
    # 🔑 自动使用 main 组的密钥: token 和 api-key 会动态解析为 primary 端点的值
    # 📋 headers 继承自 primary 端点

  # ============ 备用组 (backup) ============
  # 组定义端点 - 定义整个 backup 组使用的密钥
  - name: "backup1"
    url: "https://api.example.com"
    group: "backup"                        # 新的组名
    group-priority: 2                      # 备用组优先级
    priority: 1                            # 组内优先级
    timeout: "300s"
    token: "sk-backup-group-api-key"       # 🔑 此密钥会被同组其他端点共享
    api-key: "backup-group-api-key"        # 🔑 此API密钥会被同组其他端点共享
    headers:
      Authorization: "Bearer custom-token"
      X-API-Version: "2024-01"

  # 备用组的第二个端点 - 自动使用 backup 组的密钥
  - name: "backup2"
    url: "https://api.backup2.com"
    priority: 2                            # 组内优先级 2
    timeout: "300s"
    # 🔄 自动继承: group: "backup", group-priority: 2
    # 🔑 自动使用 backup 组的密钥: 会动态解析为 backup1 端点的密钥

  # 备用组的第三个端点 - 使用自定义密钥覆盖组默认值
  - name: "backup3"
    url: "https://api.backup3.com"
    priority: 3                            # 组内优先级 3
    timeout: "300s"
    token: "sk-custom-override-key"        # 🔑 覆盖组默认密钥，只有这个端点使用此密钥
    # 🔄 group 和 group-priority 仍然继承自 backup 组
    # 🔑 api-key 仍然使用组默认值 (backup1 的 api-key)

  # ============ 本地组 (local) ============
  # 本地服务组 - 通常不需要密钥
  - name: "local"
    url: "http://localhost:11434"
    group: "local"                         # 本地组
    group-priority: 3                      # 最低组优先级
    priority: 1                            # 组内优先级
    timeout: "300s"
    # 🔓 本地服务通常不需要 token 和 api-key

  # 本地组备用端点
  - name: "local_backup"
    url: "http://localhost:8080"
    priority: 2                            # 组内优先级 2
    timeout: "300s"
    # 🔄 自动继承: group: "local", group-priority: 3
    # 🔓 无密钥配置，适用于本地服务
// Claude Request Forwarder Web Interface JavaScript

class WebInterface {
    constructor() {
        this.refreshInterval = null;
        this.currentTab = 'overview';
        this.sseConnection = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 2000; // 2秒
        this.connectionStatus = 'disconnected';
        
        // 数据缓存，用于存储各个标签页的最新数据
        this.cachedData = {
            status: null,
            endpoints: null,
            groups: null,
            connections: null,
            logs: null,
            config: null
        };
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.showTab('overview');
        // 立即加载初始数据，不等待SSE连接
        this.loadAllTabsData();
        this.createConnectionIndicator();
        // SSE连接放在最后建立
        this.setupSSE();
    }

    bindEvents() {
        // 标签页切换事件
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('onclick').match(/'([^']+)'/)[1];
                this.showTab(tabName);
            });
        });
    }

    showTab(tabName) {
        // 隐藏所有标签页内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // 移除所有标签页的活动状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // 显示选中的标签页
        const selectedTab = document.getElementById(tabName);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // 激活对应的标签按钮
        document.querySelectorAll('.nav-tab').forEach(tab => {
            const tabTarget = tab.getAttribute('onclick')?.match(/'([^']+)'/)?.[1];
            if (tabTarget === tabName) {
                tab.classList.add('active');
            }
        });

        this.currentTab = tabName;
        
        // 优先使用缓存数据，如果没有缓存则请求API
        this.loadTabDataFromCache(tabName);
    }
    
    loadTabDataFromCache(tabName) {
        console.log('[Cache] 尝试从缓存加载标签页数据:', tabName);
        
        switch (tabName) {
            case 'overview':
                // 概览页面需要综合数据，总是重新加载
                this.loadOverview();
                break;
            case 'endpoints':
                if (this.cachedData.endpoints) {
                    console.log('[Cache] 使用缓存数据显示endpoints');
                    const container = document.getElementById('endpoints-table');
                    if (container) {
                        container.innerHTML = this.generateEndpointsTable(this.cachedData.endpoints.endpoints);
                        this.bindEndpointEvents();
                    }
                } else {
                    console.log('[Cache] 无缓存数据，请求endpoints API');
                    this.loadEndpoints();
                }
                break;
            case 'groups':
                if (this.cachedData.groups) {
                    console.log('[Cache] 使用缓存数据显示groups');
                    this.displayGroups(this.cachedData.groups);
                } else {
                    console.log('[Cache] 无缓存数据，请求groups API');
                    this.loadGroups();
                }
                break;
            case 'connections':
                if (this.cachedData.connections) {
                    console.log('[Cache] 使用缓存数据显示connections');
                    const container = document.getElementById('connections-stats');
                    if (container) {
                        container.innerHTML = this.generateConnectionsStats(this.cachedData.connections);
                    }
                } else {
                    console.log('[Cache] 无缓存数据，请求connections API');
                    this.loadConnections();
                }
                break;
            case 'logs':
                // 日志数据总是重新加载以获取最新内容
                this.loadLogs();
                break;
            case 'config':
                // 配置数据总是重新加载以确保最新
                this.loadConfig();
                break;
            default:
                // 后备方案，使用原有逻辑
                this.loadTabData(tabName);
        }
    }

    loadAllTabsData() {
        // 并行加载所有标签页数据，加快初始显示速度
        Promise.all([
            this.loadOverview(),
            this.loadEndpoints(),
            this.loadGroups(),
            this.loadConnections(),
            this.loadLogs(),
            this.loadConfig()
        ]).catch(error => {
            console.error('加载初始数据失败:', error);
        });
    }

    loadTabData(tabName) {
        switch (tabName) {
            case 'overview':
                this.loadOverview();
                break;
            case 'endpoints':
                this.loadEndpoints();
                break;
            case 'groups':
                this.loadGroups();
                break;
            case 'connections':
                this.loadConnections();
                break;
            case 'logs':
                this.loadLogs();
                break;
            case 'config':
                this.loadConfig();
                break;
        }
    }

    async loadOverview() {
        try {
            const [statusResponse, endpointsResponse, connectionsResponse, groupsResponse] = await Promise.all([
                fetch('/api/v1/status'),
                fetch('/api/v1/endpoints'),
                fetch('/api/v1/connections'),
                fetch('/api/v1/groups')
            ]);

            const status = await statusResponse.json();
            const endpoints = await endpointsResponse.json();
            const connections = await connectionsResponse.json();
            const groups = await groupsResponse.json();

            // 更新概览卡片
            document.getElementById('server-status').textContent = 
                status.status === 'running' ? '🟢 运行中' : '🔴 已停止';
            document.getElementById('uptime').textContent = status.uptime;
            document.getElementById('endpoint-count').textContent = endpoints.total;
            document.getElementById('total-requests').textContent = connections.total_requests;

            // 更新挂起请求信息
            const suspendedData = connections.suspended || {};
            const suspendedElement = document.getElementById('suspended-requests');
            const suspendedRateElement = document.getElementById('suspended-success-rate');
            
            if (suspendedElement) {
                suspendedElement.textContent = `${suspendedData.suspended_requests || 0} / ${suspendedData.total_suspended_requests || 0}`;
            }
            
            if (suspendedRateElement) {
                const rate = suspendedData.success_rate || 0;
                suspendedRateElement.textContent = `成功率: ${rate.toFixed(1)}%`;
                suspendedRateElement.className = rate > 80 ? 'text-muted' : 'text-warning';
            }

            // 更新当前活动组信息
            const activeGroupElement = document.getElementById('active-group');
            const groupSuspendedInfoElement = document.getElementById('group-suspended-info');
            
            if (activeGroupElement) {
                activeGroupElement.textContent = groups.active_group || '未知';
            }
            
            if (groupSuspendedInfoElement && groups.total_suspended_requests > 0) {
                groupSuspendedInfoElement.textContent = `${groups.total_suspended_requests} 个挂起请求`;
                groupSuspendedInfoElement.style.display = 'block';
            } else if (groupSuspendedInfoElement) {
                groupSuspendedInfoElement.style.display = 'none';
            }

        } catch (error) {
            console.error('加载概览数据失败:', error);
            this.showError('概览数据加载失败');
        }
    }

    async loadEndpoints() {
        try {
            const response = await fetch('/api/v1/endpoints');
            const data = await response.json();

            // 更新缓存
            this.cachedData.endpoints = data;
            console.log('[API] endpoints数据已加载并缓存');

            const container = document.getElementById('endpoints-table');
            if (data.endpoints && data.endpoints.length > 0) {
                container.innerHTML = this.generateEndpointsTable(data.endpoints);
                this.bindEndpointEvents();
            } else {
                container.innerHTML = '<p>暂无端点数据</p>';
            }
        } catch (error) {
            console.error('加载端点数据失败:', error);
            this.showError('端点数据加载失败');
        }
    }

    generateEndpointsTable(endpoints) {
        let html = `
            <table>
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>名称</th>
                        <th>URL</th>
                        <th>优先级</th>
                        <th>组</th>
                        <th>响应时间</th>
                        <th>最后检查</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
        `;

        endpoints.forEach(endpoint => {
            let statusClass, statusText;
            
            // 根据never_checked字段决定状态显示
            if (endpoint.never_checked) {
                statusClass = 'status-never-checked';
                statusText = '未检测';
            } else if (endpoint.healthy) {
                statusClass = 'status-healthy';
                statusText = '健康';
            } else {
                statusClass = 'status-unhealthy';
                statusText = '不健康';
            }
            
            html += `
                <tr>
                    <td>
                        <span class="status-indicator ${statusClass}"></span>
                        ${statusText}
                    </td>
                    <td>${endpoint.name}</td>
                    <td>${endpoint.url}</td>
                    <td>
                        <input type="number" 
                               class="priority-input" 
                               value="${endpoint.priority}" 
                               data-endpoint="${endpoint.name}"
                               min="1">
                    </td>
                    <td>${endpoint.group} (${endpoint.group_priority})</td>
                    <td>${endpoint.response_time}</td>
                    <td>${endpoint.last_check}</td>
                    <td>
                        <button class="btn btn-sm update-priority" data-endpoint="${endpoint.name}">
                            更新
                        </button>
                        <button class="btn btn-sm manual-health-check" data-endpoint="${endpoint.name}" title="手动健康检测">
                            检测
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        return html;
    }

    bindEndpointEvents() {
        // 绑定优先级更新按钮事件
        document.querySelectorAll('.update-priority').forEach(button => {
            button.addEventListener('click', async (e) => {
                const endpointName = e.target.dataset.endpoint;
                const priorityInput = document.querySelector(`input[data-endpoint="${endpointName}"]`);
                const newPriority = parseInt(priorityInput.value);

                if (newPriority < 1) {
                    alert('优先级必须大于等于1');
                    return;
                }

                try {
                    const response = await fetch(`/api/v1/endpoints/${endpointName}/priority`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ priority: newPriority })
                    });

                    const result = await response.json();
                    if (result.success) {
                        this.showSuccess('优先级更新成功');
                        this.loadEndpoints(); // 重新加载端点数据
                    } else {
                        this.showError(result.error || '更新失败');
                    }
                } catch (error) {
                    console.error('更新优先级失败:', error);
                    this.showError('更新优先级失败');
                }
            });
        });

        // 绑定回车键更新事件
        document.querySelectorAll('.priority-input').forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const updateButton = document.querySelector(`.update-priority[data-endpoint="${input.dataset.endpoint}"]`);
                    updateButton.click();
                }
            });
        });

        // 绑定手动健康检测按钮事件
        document.querySelectorAll('.manual-health-check').forEach(button => {
            button.addEventListener('click', async (e) => {
                const endpointName = e.target.dataset.endpoint;
                const originalText = e.target.innerHTML;
                
                // 显示加载状态
                e.target.innerHTML = '检测中...';
                e.target.disabled = true;

                try {
                    const response = await fetch(`/api/v1/endpoints/${endpointName}/health-check`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const result = await response.json();
                    if (result.success) {
                        const healthText = result.healthy ? '健康' : '不健康';
                        this.showSuccess(`手动检测完成 - ${endpointName}: ${healthText}`);
                        this.loadEndpoints(); // 重新加载端点数据
                    } else {
                        this.showError(result.error || '手动检测失败');
                    }
                } catch (error) {
                    console.error('手动检测失败:', error);
                    this.showError('手动检测失败');
                } finally {
                    // 恢复按钮状态
                    e.target.innerHTML = originalText;
                    e.target.disabled = false;
                }
            });
        });
    }

    async loadGroups() {
        try {
            const response = await fetch('/api/v1/groups');
            if (!response.ok) {
                throw new Error('获取组信息失败');
            }
            const data = await response.json();
            this.displayGroups(data);
        } catch (error) {
            console.error('加载组信息失败:', error);
            document.getElementById('groups-container').innerHTML = 
                '<div class="error">❌ 加载组信息失败: ' + error.message + '</div>';
        }
    }

    displayGroups(data) {
        // 显示组信息概要卡片
        const groupInfoCards = document.getElementById('group-info-cards');
        if (data.groups && data.groups.length > 0) {
            groupInfoCards.innerHTML = data.groups.map(group => this.createGroupCard(group)).join('');
        } else {
            groupInfoCards.innerHTML = '<div class="info">📦 没有配置的组</div>';
        }

        // 显示组统计概要
        const groupsContainer = document.getElementById('groups-container');
        const summaryHtml = `
            <div class="groups-summary">
                <div class="summary-item">
                    <div class="summary-value">${data.total_groups || 0}</div>
                    <div class="summary-label">总组数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">${data.active_groups || 0}</div>
                    <div class="summary-label">活跃组数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">${data.groups ? data.groups.reduce((sum, g) => sum + g.healthy_endpoints, 0) : 0}</div>
                    <div class="summary-label">健康端点</div>
                </div>
            </div>
        `;
        groupsContainer.innerHTML = summaryHtml;
    }

    createGroupCard(group) {
        const statusClass = group.in_cooldown ? 'cooldown' : (group.is_active ? 'active' : 'inactive');
        const statusText = group.status || (group.in_cooldown ? '冷却中' : (group.is_active ? '活跃' : '未激活'));
        
        const cooldownInfo = group.in_cooldown && group.cooldown_remaining !== '0s' ? 
            `<div class="group-cooldown-info">🕐 冷却剩余时间: ${group.cooldown_remaining}</div>` : '';

        return `
            <div class="group-info-card ${statusClass}">
                <div class="group-card-header">
                    <h3 class="group-name">${group.name}</h3>
                    <span class="group-status ${statusClass}">${statusText}</span>
                </div>
                <div class="group-details">
                    <div class="group-detail-item">
                        <div class="group-detail-label">优先级</div>
                        <div class="group-detail-value group-priority">${group.priority}</div>
                    </div>
                    <div class="group-detail-item">
                        <div class="group-detail-label">端点总数</div>
                        <div class="group-detail-value">${group.total_endpoints}</div>
                    </div>
                    <div class="group-detail-item">
                        <div class="group-detail-label">健康端点</div>
                        <div class="group-detail-value group-endpoints-count">${group.healthy_endpoints}</div>
                    </div>
                    <div class="group-detail-item">
                        <div class="group-detail-label">不健康端点</div>
                        <div class="group-detail-value">${group.unhealthy_endpoints}</div>
                    </div>
                </div>
                <div class="group-actions">
                    <button class="group-btn btn-activate" 
                            onclick="webInterface.activateGroup('${group.name}')" 
                            ${!group.can_activate ? 'disabled' : ''}>
                        🚀 激活
                    </button>
                    <button class="group-btn btn-pause" 
                            onclick="webInterface.pauseGroup('${group.name}')" 
                            ${!group.can_pause ? 'disabled' : ''}>
                        ⏸️ 暂停
                    </button>
                    <button class="group-btn btn-resume" 
                            onclick="webInterface.resumeGroup('${group.name}')" 
                            ${!group.can_resume ? 'disabled' : ''}>
                        ▶️ 恢复
                    </button>
                </div>
                ${cooldownInfo}
            </div>
        `;
    }

    async activateGroup(groupName) {
        try {
            const response = await fetch(`/api/v1/groups/${groupName}/activate`, {
                method: 'POST'
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '激活组失败');
            }
            const result = await response.json();
            this.showNotification(result.message || `组 ${groupName} 已激活`, 'success');
            // 刷新组数据
            this.loadGroups();
        } catch (error) {
            console.error('激活组失败:', error);
            this.showNotification('激活组失败: ' + error.message, 'error');
        }
    }

    async pauseGroup(groupName) {
        try {
            const response = await fetch(`/api/v1/groups/${groupName}/pause`, {
                method: 'POST'
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '暂停组失败');
            }
            const result = await response.json();
            this.showNotification(result.message || `组 ${groupName} 已暂停`, 'success');
            // 刷新组数据
            this.loadGroups();
        } catch (error) {
            console.error('暂停组失败:', error);
            this.showNotification('暂停组失败: ' + error.message, 'error');
        }
    }

    async resumeGroup(groupName) {
        try {
            const response = await fetch(`/api/v1/groups/${groupName}/resume`, {
                method: 'POST'
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '恢复组失败');
            }
            const result = await response.json();
            this.showNotification(result.message || `组 ${groupName} 已恢复`, 'success');
            // 刷新组数据
            this.loadGroups();
        } catch (error) {
            console.error('恢复组失败:', error);
            this.showNotification('恢复组失败: ' + error.message, 'error');
        }
    }

    async loadConnections() {
        try {
            const response = await fetch('/api/v1/connections');
            const data = await response.json();

            // 更新基础连接统计
            const container = document.getElementById('connections-stats');
            container.innerHTML = this.generateConnectionsStats(data);

            // 更新挂起请求统计
            this.updateSuspendedStats(data.suspended || {});
            
            // 更新挂起连接列表
            this.updateSuspendedConnections(data.suspended_connections || []);
        } catch (error) {
            console.error('加载连接数据失败:', error);
            this.showError('连接数据加载失败');
        }
    }

    generateConnectionsStats(data) {
        return `
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">${data.total_requests || 0}</div>
                    <div class="stat-label">总请求数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${data.active_connections || 0}</div>
                    <div class="stat-label">活跃连接</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${data.successful_requests || 0}</div>
                    <div class="stat-label">成功请求</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${data.failed_requests || 0}</div>
                    <div class="stat-label">失败请求</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${data.average_response_time || '0s'}</div>
                    <div class="stat-label">平均响应时间</div>
                </div>
            </div>
        `;
    }

    async loadLogs() {
        try {
            const response = await fetch('/api/v1/logs');
            const data = await response.json();

            const container = document.getElementById('logs-container');
            if (data.logs && data.logs.length > 0) {
                container.innerHTML = this.generateLogsDisplay(data.logs);
            } else {
                container.innerHTML = '<p>暂无日志数据</p>';
            }
        } catch (error) {
            console.error('加载日志数据失败:', error);
            this.showError('日志数据加载失败');
        }
    }

    generateLogsDisplay(logs) {
        let html = '<div class="logs-container">';
        
        logs.forEach(log => {
            html += `
                <div class="log-entry">
                    <span class="log-timestamp">${log.timestamp}</span>
                    <span class="log-level ${log.level}">${log.level}</span>
                    <span class="log-message">${log.message}</span>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    async loadConfig() {
        try {
            const response = await fetch('/api/v1/config');
            const data = await response.json();

            const container = document.getElementById('config-display');
            container.innerHTML = this.generateConfigDisplay(data);
        } catch (error) {
            console.error('加载配置数据失败:', error);
            this.showError('配置数据加载失败');
        }
    }

    generateConfigDisplay(config) {
        let html = '';

        Object.keys(config).forEach(section => {
            if (typeof config[section] === 'object' && config[section] !== null) {
                html += `<div class="config-section">`;
                html += `<h3>${section}</h3>`;
                
                Object.keys(config[section]).forEach(key => {
                    let value = config[section][key];
                    if (typeof value === 'object') {
                        value = JSON.stringify(value, null, 2);
                    }
                    
                    html += `
                        <div class="config-item">
                            <span class="config-key">${key}</span>
                            <span class="config-value">${value}</span>
                        </div>
                    `;
                });
                
                html += `</div>`;
            }
        });

        return html;
    }

    startAutoRefresh() {
        // SSE连接建立后不再需要定时刷新
        // 但为了兼容性保留，在SSE连接失败时使用
        if (this.connectionStatus === 'connected') {
            this.stopAutoRefresh();
            return;
        }
        
        // 每30秒自动刷新当前标签页数据
        this.refreshInterval = setInterval(() => {
            if (this.connectionStatus !== 'connected') {
                this.loadTabData(this.currentTab);
            }
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    showSuccess(message) {
        this.showNotification('✅ ' + message, 'success');
    }

    showError(message) {
        this.showNotification('❌ ' + message, 'error');
    }
    
    showInfo(message) {
        this.showNotification('ℹ️ ' + message, 'info');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        const colors = {
            success: 'var(--success-color, #10b981)',
            error: 'var(--error-color, #ef4444)',
            info: 'var(--info-color, #3b82f6)'
        };
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type]};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 400px;
        `;
        
        document.body.appendChild(notification);
        
        const timeout = type === 'error' ? 5000 : 3000;
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, timeout);
    }

    // SSE相关方法
    setupSSE() {
        this.connectSSE();
    }

    connectSSE() {
        if (this.sseConnection) {
            this.sseConnection.close();
        }

        this.updateConnectionStatus('connecting');
        
        const clientId = this.getOrCreateClientId();
        const events = 'status,endpoint,group,connection,log,chart'; // 订阅的事件类型，包括图表事件
        
        try {
            this.sseConnection = new EventSource(`/api/v1/stream?client_id=${clientId}&events=${events}`);
            
            this.sseConnection.onopen = () => {
                console.log('SSE连接已建立');
                this.updateConnectionStatus('connected');
                this.reconnectAttempts = 0;
                this.stopAutoRefresh(); // 停止定时刷新
            };
            
            this.sseConnection.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleSSEMessage(data);
                } catch (error) {
                    console.error('解析SSE消息失败:', error, event.data);
                }
            };
            
            // 监听特定事件类型
            ['status', 'endpoint', 'group', 'connection', 'log', 'config', 'chart'].forEach(eventType => {
                this.sseConnection.addEventListener(eventType, (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleSSEMessage(data, eventType);
                    } catch (error) {
                        console.error(`解析${eventType}事件失败:`, error);
                    }
                });
            });
            
            this.sseConnection.onerror = (event) => {
                console.error('SSE连接错误:', event);
                this.updateConnectionStatus('error');
                this.handleSSEError();
            };
            
        } catch (error) {
            console.error('创建SSE连接失败:', error);
            this.updateConnectionStatus('error');
            this.handleSSEError();
        }
    }

    handleSSEMessage(data, eventType) {
        // 根据事件类型更新相应的UI
        switch (eventType || data.type) {
            case 'status':
                this.updateStatusData(data);
                break;
            case 'endpoint':
                this.updateEndpointsData(data);
                break;
            case 'group':
                this.updateGroupsData(data);
                break;
            case 'connection':
                this.updateConnectionsData(data);
                break;
            case 'suspended':
                this.updateSuspendedData(data);
                break;
            case 'log':
                this.updateLogsData(data);
                break;
            case 'config':
                this.showInfo('配置已更新');
                if (this.currentTab === 'config') {
                    this.loadConfig();
                }
                break;
            case 'chart':
                this.updateChartData(data);
                break;
            default:
                console.log('收到SSE消息:', data);
        }
    }

    handleSSEError() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * this.reconnectAttempts;
            
            console.log(`SSE重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}，${delay}ms后重试`);
            this.updateConnectionStatus('reconnecting');
            
            setTimeout(() => {
                this.connectSSE();
            }, delay);
        } else {
            console.error('SSE重连尝试已达上限，切换到定时刷新模式');
            this.updateConnectionStatus('failed');
            this.startAutoRefresh(); // 回退到定时刷新
        }
    }

    updateConnectionStatus(status) {
        this.connectionStatus = status;
        const indicator = document.getElementById('connection-indicator');
        if (indicator) {
            const statusInfo = this.getConnectionStatusInfo(status);
            indicator.className = `connection-indicator ${status}`;
            indicator.textContent = statusInfo.text;
            indicator.title = statusInfo.tooltip;
        }
    }

    getConnectionStatusInfo(status) {
        const statusMap = {
            'connected': { text: '🟢', tooltip: 'SSE连接已建立，实时更新中' },
            'connecting': { text: '🟡', tooltip: '正在连接...' },
            'reconnecting': { text: '🟠', tooltip: `重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})` },
            'error': { text: '🔴', tooltip: 'SSE连接错误' },
            'failed': { text: '⚫', tooltip: 'SSE连接失败，使用定时刷新' },
            'disconnected': { text: '⚪', tooltip: '未连接' }
        };
        return statusMap[status] || statusMap['disconnected'];
    }

    createConnectionIndicator() {
        const header = document.querySelector('header');
        if (header) {
            const indicator = document.createElement('div');
            indicator.id = 'connection-indicator';
            indicator.className = 'connection-indicator disconnected';
            indicator.textContent = '⚪';
            indicator.title = '连接状态指示器';
            indicator.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                font-size: 20px;
                cursor: help;
            `;
            header.appendChild(indicator);
        }
    }

    getOrCreateClientId() {
        let clientId = localStorage.getItem('sse_client_id');
        if (!clientId) {
            clientId = 'client_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
            localStorage.setItem('sse_client_id', clientId);
        }
        return clientId;
    }

    // 数据更新方法
    updateStatusData(data) {
        if (this.currentTab === 'overview') {
            if (data.status) document.getElementById('server-status').textContent = 
                data.status === 'running' ? '🟢 运行中' : '🔴 已停止';
            if (data.uptime) document.getElementById('uptime').textContent = data.uptime;
        }
    }

    updateEndpointsData(data) {
        // 始终更新缓存数据，不管当前在哪个标签页
        if (data.endpoints) {
            this.cachedData.endpoints = data;
            console.log('[SSE] 端点数据已更新到缓存:', data.endpoints.length, '个端点');
        }
        
        // 如果当前在endpoints标签页，立即更新UI
        if (this.currentTab === 'endpoints' && data.endpoints) {
            const container = document.getElementById('endpoints-table');
            if (container) {
                container.innerHTML = this.generateEndpointsTable(data.endpoints);
                this.bindEndpointEvents();
                console.log('[UI] endpoints标签页UI已更新');
            }
        }
        
        // 更新概览页面的端点数量（始终更新）
        if (data.total !== undefined) {
            const endpointCount = document.getElementById('endpoint-count');
            if (endpointCount && this.currentTab === 'overview') {
                endpointCount.textContent = data.total;
                console.log('[UI] 概览页面端点数量已更新:', data.total);
            }
        }
    }

    updateGroupsData(data) {
        // 始终更新缓存数据
        if (data) {
            this.cachedData.groups = data;
            console.log('[SSE] 组数据已更新到缓存');
            
            // 更新挂起请求提示
            this.updateGroupSuspendedAlert(data);
        }
        
        // 如果当前在groups标签页，立即更新UI
        if (this.currentTab === 'groups') {
            this.displayGroups(data);
            console.log('[UI] groups标签页UI已更新');
        }
    }

    updateConnectionsData(data) {
        // 始终更新缓存数据
        if (data) {
            this.cachedData.connections = data;
            console.log('[SSE] 连接数据已更新到缓存');
        }
        
        // 如果当前在connections标签页，立即更新UI
        if (this.currentTab === 'connections') {
            const container = document.getElementById('connections-stats');
            if (container) {
                container.innerHTML = this.generateConnectionsStats(data);
                console.log('[UI] connections标签页UI已更新');
            }
            
            // 更新挂起请求统计
            if (data.suspended) {
                this.updateSuspendedStats(data.suspended);
            }
            
            // 更新挂起连接列表
            if (data.suspended_connections) {
                this.updateSuspendedConnections(data.suspended_connections);
            }
        }
        
        // 如果在概览页面，更新挂起请求信息
        if (this.currentTab === 'overview' && data.suspended) {
            const suspendedElement = document.getElementById('suspended-requests');
            const suspendedRateElement = document.getElementById('suspended-success-rate');
            
            if (suspendedElement) {
                suspendedElement.textContent = `${data.suspended.suspended_requests || 0} / ${data.suspended.total_suspended_requests || 0}`;
            }
            
            if (suspendedRateElement) {
                const rate = data.suspended.success_rate || 0;
                suspendedRateElement.textContent = `成功率: ${rate.toFixed(1)}%`;
                suspendedRateElement.className = rate > 80 ? 'text-muted' : 'text-warning';
            }
        }
    }
        
        // 更新概览页面的请求数（始终更新）
        if (data.total_requests !== undefined) {
            const totalRequests = document.getElementById('total-requests');
            if (totalRequests && this.currentTab === 'overview') {
                totalRequests.textContent = data.total_requests;
                console.log('[UI] 概览页面请求总数已更新:', data.total_requests);
            }
        }
    }

    updateSuspendedData(data) {
        console.log('[SSE] 收到挂起请求事件数据:', data);
        
        // 如果在连接标签页，更新挂起请求统计
        if (this.currentTab === 'connections') {
            if (data.current) {
                this.updateSuspendedStats(data.current);
            }
            if (data.suspended_connections) {
                this.updateSuspendedConnections(data.suspended_connections);
            }
        }
        
        // 在概览页面更新挂起请求统计
        if (this.currentTab === 'overview' && data.current) {
            const suspendedElement = document.getElementById('suspended-requests');
            const suspendedRateElement = document.getElementById('suspended-success-rate');
            
            if (suspendedElement) {
                suspendedElement.textContent = `${data.current.suspended_requests || 0} / ${data.current.total_suspended_requests || 0}`;
            }
            
            if (suspendedRateElement) {
                const rate = data.current.success_rate || 0;
                suspendedRateElement.textContent = `成功率: ${rate.toFixed(1)}%`;
                suspendedRateElement.className = rate > 80 ? 'text-muted' : 'text-warning';
            }
        }
        
        // 显示挂起请求通知
        if (data.current && data.current.suspended_requests > 0) {
            this.showNotification(`当前有 ${data.current.suspended_requests} 个挂起请求`, 'info');
        }
    }

    updateLogsData(data) {
        if (this.currentTab === 'logs' && data.logs) {
            const container = document.getElementById('logs-container');
            if (container) {
                // 在现有日志前面添加新日志
                const newLogHtml = this.generateLogsDisplay([data]);
                container.insertAdjacentHTML('afterbegin', newLogHtml);
                
                // 限制日志条数，避免页面过载
                const logEntries = container.querySelectorAll('.log-entry');
                if (logEntries.length > 100) {
                    for (let i = 100; i < logEntries.length; i++) {
                        logEntries[i].remove();
                    }
                }
            }
        }
    }

    // 更新图表数据
    updateChartData(data) {
        // 通知图表管理器处理SSE更新
        if (window.chartManager) {
            try {
                // 启用SSE更新模式
                if (!window.chartManager.sseEnabled) {
                    window.chartManager.enableSSEUpdates();
                }
                
                // 发送图表更新事件到图表管理器
                const chartUpdateEvent = new CustomEvent('chartUpdate', {
                    detail: {
                        chart_type: data.chart_type,
                        data: data.data
                    }
                });
                document.dispatchEvent(chartUpdateEvent);
                
                console.log(`📊 SSE图表数据更新: ${data.chart_type}`);
            } catch (error) {
                console.error('更新图表数据失败:', error);
                // 回退到直接更新模式
                this.updateChartLegacy(data);
            }
        } else {
            console.warn('图表管理器未初始化');
        }
    }

    // 兼容旧版图表更新（作为备用方案）
    updateChartLegacy(data) {
        if (this.currentTab === 'charts' && window.chartManager) {
            try {
                const chartType = data.chart_type;
                const chartData = data.data;
                
                // 根据图表类型更新对应的图表
                const chartName = this.mapChartTypeToName(chartType);
                if (chartName && window.chartManager.charts.has(chartName)) {
                    const chart = window.chartManager.charts.get(chartName);
                    chart.data = chartData;
                    chart.update('none'); // 无动画更新，实时性更好
                }
            } catch (error) {
                console.error('兼容模式图表更新失败:', error);
            }
        }
    }

    // 映射图表类型到内部名称
    mapChartTypeToName(chartType) {
        const mapping = {
            'request_trends': 'requestTrend',
            'response_times': 'responseTime', 
            'token_usage': 'tokenUsage',
            'endpoint_health': 'endpointHealth',
            'connection_activity': 'connectionActivity',
            'endpoint_performance': 'endpointPerformance'
        };
        return mapping[chartType] || chartType;
    }

    // 清理资源
    destroy() {
        if (this.sseConnection) {
            this.sseConnection.close();
        }
        this.stopAutoRefresh();
    }

    // 更新挂起请求统计
    updateSuspendedStats(suspendedData) {
        const elements = {
            'current-suspended': suspendedData.suspended_requests || 0,
            'total-suspended': suspendedData.total_suspended_requests || 0,
            'successful-suspended': suspendedData.successful_suspended_requests || 0,
            'timeout-suspended': suspendedData.timeout_suspended_requests || 0,
            'suspended-success-rate-detail': `${(suspendedData.success_rate || 0).toFixed(1)}%`,
            'avg-suspended-time': suspendedData.average_suspended_time || '0ms'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    // 更新挂起连接列表
    updateSuspendedConnections(connections) {
        const container = document.getElementById('suspended-connections-table');
        if (!container) return;

        if (connections.length === 0) {
            container.innerHTML = '<p>无挂起连接</p>';
            return;
        }

        let html = '<div class="suspended-connections-list">';
        connections.forEach(conn => {
            html += `
                <div class="suspended-connection-item">
                    <div class="connection-header">
                        <div class="connection-id">${conn.id}</div>
                        <div class="suspended-time">${conn.suspended_time}</div>
                    </div>
                    <div class="connection-details">
                        <div><strong>IP:</strong> ${conn.client_ip}</div>
                        <div><strong>端点:</strong> ${conn.endpoint}</div>
                        <div><strong>方法:</strong> ${conn.method}</div>
                        <div><strong>路径:</strong> ${conn.path}</div>
                        <div><strong>重试次数:</strong> ${conn.retry_count}</div>
                        <div><strong>挂起时间:</strong> ${conn.suspended_at}</div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        container.innerHTML = html;
    }

    // 更新组管理界面的挂起提示
    updateGroupSuspendedAlert(groupData) {
        const alertBanner = document.getElementById('group-suspended-alert');
        const alertMessage = document.getElementById('suspended-alert-message');
        
        if (!alertBanner || !alertMessage) return;

        const totalSuspended = groupData.total_suspended_requests || 0;
        const groupCounts = groupData.group_suspended_counts || {};

        if (totalSuspended > 0) {
            let message = `当前有 ${totalSuspended} 个挂起请求等待处理`;
            const suspendedGroups = Object.entries(groupCounts)
                .filter(([group, count]) => count > 0)
                .map(([group, count]) => `${group}(${count})`)
                .join(', ');
            
            if (suspendedGroups) {
                message += `，涉及组: ${suspendedGroups}`;
            }
            
            alertMessage.textContent = message;
            alertBanner.style.display = 'flex';
        } else {
            alertBanner.style.display = 'none';
        }
    }
}

// 全局函数用于HTML中的onclick事件
function showTab(tabName) {
    // 添加安全检查和更好的错误处理
    try {
        if (window.webInterface && window.webInterface.showTab) {
            window.webInterface.showTab(tabName);
        } else {
            console.warn('WebInterface not ready yet, retrying in 100ms...');
            // 如果webInterface还没准备好，延迟重试
            setTimeout(() => showTab(tabName), 100);
        }
    } catch (error) {
        console.error('Error in showTab:', error);
    }
}

// 隐藏挂起请求警告
function hideSuspendedAlert() {
    const alertBanner = document.getElementById('group-suspended-alert');
    if (alertBanner) {
        alertBanner.style.display = 'none';
    }
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.webInterface) {
        window.webInterface.destroy();
    }
});

// 初始化Web界面
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔄 DOM内容已加载，开始初始化WebInterface...');
    try {
        window.webInterface = new WebInterface();
        console.log('✅ WebInterface初始化成功');
        
        // 验证showTab函数是否可用
        if (typeof window.webInterface.showTab === 'function') {
            console.log('✅ showTab方法可用');
        } else {
            console.error('❌ showTab方法不可用');
        }
    } catch (error) {
        console.error('❌ WebInterface初始化失败:', error);
    }
});
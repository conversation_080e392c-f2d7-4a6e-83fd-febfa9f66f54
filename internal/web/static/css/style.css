/* <PERSON> Request Forwarder Web Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --bg-color: #f8fafc;
    --card-bg: #ffffff;
    --border-color: #e2e8f0;
    --text-color: #1e293b;
    --text-muted: #64748b;
    --chart-grid-color: rgba(0, 0, 0, 0.1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    position: relative;
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

header h1 {
    color: var(--primary-color);
    font-size: 2.5em;
    margin-bottom: 8px;
}

header p {
    color: var(--text-muted);
    font-size: 1.1em;
}

.nav-tabs {
    display: flex;
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow-x: auto;
    overflow-y: hidden;
}

.nav-tab {
    flex: 1;
    background: none;
    border: none;
    padding: 15px 20px;
    font-size: 1em;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    min-width: fit-content;
}

.nav-tab:hover {
    background-color: #f1f5f9;
    color: var(--primary-color);
}

.nav-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: #f8fafc;
}

main {
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 图表样式增强 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.chart-container {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-height: 400px;
    position: relative;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.chart-container:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.chart-controls select {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 12px;
    background: var(--card-bg);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-controls select:hover {
    border-color: var(--primary-color);
}

.chart-controls select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.chart-controls button {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 12px;
    background: var(--card-bg);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.chart-controls button:hover {
    background: var(--bg-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.chart-controls button:active {
    transform: translateY(1px);
}

.chart-canvas {
    position: relative;
    height: 300px;
    width: 100%;
    margin-top: 10px;
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-muted);
    font-size: 14px;
    display: none;
    z-index: 10;
}

/* 连接指示器样式增强 */
.connection-indicator {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 20px;
    cursor: help;
    transition: all 0.3s ease;
    z-index: 100;
    padding: 5px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.connection-indicator.connected {
    color: var(--success-color);
}

.connection-indicator.connecting {
    color: var(--warning-color);
    animation: pulse 1s infinite;
}

.connection-indicator.reconnecting {
    color: #f97316;
    animation: pulse 1.5s infinite;
}

.connection-indicator.error {
    color: var(--error-color);
}

.connection-indicator.failed {
    color: var(--text-muted);
}

.connection-indicator.disconnected {
    color: #9ca3af;
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.5; 
        transform: scale(1.1);
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .chart-container {
        min-height: 300px;
        padding: 15px;
    }
    
    .chart-canvas {
        height: 250px;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .chart-controls {
        width: 100%;
        justify-content: flex-end;
    }
    
    .nav-tabs {
        justify-content: flex-start;
    }
    
    .nav-tab {
        padding: 12px 16px;
        font-size: 14px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .connection-indicator {
        top: 10px;
        right: 10px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .charts-grid {
        gap: 10px;
    }
    
    .chart-container {
        padding: 10px;
        min-height: 280px;
    }
    
    .chart-canvas {
        height: 220px;
    }
    
    .chart-title {
        font-size: 16px;
    }
    
    .chart-controls button,
    .chart-controls select {
        font-size: 11px;
        padding: 4px 8px;
    }
}

.cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.card {
    background: #f8fafc;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.1em;
}

.card p {
    color: var(--text-color);
    font-size: 1.2em;
    font-weight: 600;
}

.section {
    margin-bottom: 30px;
}

.section h2 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.5em;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: #f1f5f9;
    color: var(--text-color);
    font-weight: 600;
}

tr:hover {
    background: #f8fafc;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-healthy {
    background-color: var(--success-color);
}

.status-unhealthy {
    background-color: var(--error-color);
}

.status-never-checked {
    background-color: var(--warning-color);
}

.status-unknown {
    background-color: var(--secondary-color);
}

.priority-input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    text-align: center;
}

.btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background 0.2s ease;
}

.btn:hover {
    background: #1d4ed8;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.8em;
}

.btn-secondary {
    background: var(--secondary-color);
    margin-left: 5px;
}

.btn-secondary:hover {
    background: #475569;
}

.btn.manual-health-check {
    background: var(--success-color) !important;
    margin-left: 5px;
}

.btn.manual-health-check:hover {
    background: #059669 !important;
}

.logs-container {
    background: #1e293b;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    max-height: 400px;
    overflow-y: auto;
}

.log-entry {
    margin-bottom: 5px;
    padding: 5px 0;
    border-bottom: 1px solid #334155;
}

.log-timestamp {
    color: var(--secondary-color);
    margin-right: 10px;
}

.log-level {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: bold;
    margin-right: 10px;
}

.log-level.INFO {
    background: var(--primary-color);
    color: white;
}

.log-level.WARN {
    background: var(--warning-color);
    color: white;
}

.log-level.ERROR {
    background: var(--error-color);
    color: white;
}

.config-section {
    background: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.config-section h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.1em;
}

.config-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid var(--border-color);
}

.config-item:last-child {
    border-bottom: none;
}

.config-key {
    font-weight: 600;
    color: var(--text-color);
}

.config-value {
    color: var(--text-muted);
    font-family: monospace;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    background: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.stat-value {
    font-size: 1.8em;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9em;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tab {
        flex-basis: 50%;
        font-size: 0.9em;
        padding: 12px 15px;
    }
    
    .cards {
        grid-template-columns: 1fr;
    }
    
    table {
        font-size: 0.9em;
    }
    
    th, td {
        padding: 8px 10px;
    }
}

/* 组管理样式 */
.group-info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.group-info-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.group-info-card.active {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.group-info-card.inactive {
    border-color: var(--secondary-color);
    background: rgba(100, 116, 139, 0.05);
}

.group-info-card.cooldown {
    border-color: var(--warning-color);
    background: rgba(245, 158, 11, 0.05);
}

.group-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.group-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.group-name {
    font-size: 1.4em;
    font-weight: normal;
    margin: 0;
    color: var(--text-color);
}

.group-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.group-status.active {
    background: var(--success-color);
    color: white;
}

.group-status.inactive {
    background: var(--secondary-color);
    color: white;
}

.group-status.cooldown {
    background: var(--warning-color);
    color: white;
}

.group-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.group-detail-item {
    display: flex;
    flex-direction: column;
}

.group-detail-label {
    font-size: 0.85em;
    color: var(--text-muted);
    margin-bottom: 4px;
    font-weight: 500;
}

.group-detail-value {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--text-color);
}

.group-endpoints-count {
    color: var(--primary-color);
}

.group-priority {
    color: var(--secondary-color);
}

.group-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.group-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 80px;
}

.group-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.group-btn.btn-activate {
    background: var(--success-color);
    color: white;
}

.group-btn.btn-activate:hover:not(:disabled) {
    background: #059669;
}

.group-btn.btn-pause {
    background: var(--warning-color);
    color: white;
}

.group-btn.btn-pause:hover:not(:disabled) {
    background: #d97706;
}

.group-btn.btn-resume {
    background: var(--primary-color);
    color: white;
}

.group-btn.btn-resume:hover:not(:disabled) {
    background: #1d4ed8;
}

.group-cooldown-info {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
    font-size: 0.9em;
    color: #92400e;
}

.groups-container {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
}

.groups-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 20px;
}

.summary-item {
    text-align: center;
}

.summary-value {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--primary-color);
}

.summary-label {
    font-size: 0.9em;
    color: var(--text-muted);
    margin-top: 4px;
}

@media (max-width: 768px) {
    .group-info-cards {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .group-details {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .group-actions {
        flex-direction: column;
    }
    
    .group-btn {
        flex: none;
        min-width: auto;
    }
    
    .groups-summary {
        flex-direction: column;
        gap: 15px;
    }
}

/* 表格列宽优化 */
.requests-table th:nth-child(1) { width: 120px; } /* 请求ID */
.requests-table th:nth-child(2) { width: 140px; } /* 时间 */
.requests-table th:nth-child(3) { width: 60px; }  /* 状态 */
.requests-table th:nth-child(4) { width: 180px; } /* 模型 */
.requests-table th:nth-child(5) { width: 120px; } /* 端点 */
.requests-table th:nth-child(6) { width: 60px; }  /* 组 */
.requests-table th:nth-child(7) { width: 70px; }  /* 耗时 */
.requests-table th:nth-child(8) { width: 60px; }  /* 输入 */
.requests-table th:nth-child(9) { width: 60px; }  /* 输出 */
.requests-table th:nth-child(10) { width: 80px; } /* 缓存创建 */
.requests-table th:nth-child(11) { width: 80px; } /* 缓存读取 */
.requests-table th:nth-child(12) { width: 80px; } /* 成本 */
.requests-table th:nth-child(13) { width: 80px; } /* 操作 */

/* Token列样式 */
.input-tokens, .output-tokens, .cache-creation-tokens, .cache-read-tokens {
    text-align: right;
    font-family: monospace;
}

/* 表格中的组名使用正常字体大小 */
.requests-table .group-name {
    font-size: 1em;
    font-weight: normal;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
    backdrop-filter: blur(2px);
}

.modal-content {
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.4em;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-muted);
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-color);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px 20px;
    border-top: 1px solid var(--border-color);
    background: #fafbfc;
    border-radius: 0 0 12px 12px;
}

/* 详情区域样式 */
.detail-section {
    margin-bottom: 24px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section-title {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid var(--primary-color);
    display: inline-block;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item label {
    font-size: 0.9em;
    font-weight: 500;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    font-size: 1em;
    color: var(--text-color);
    word-break: break-all;
}

.detail-value code {
    background: rgba(37, 99, 235, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.9em;
    color: var(--primary-color);
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.user-agent {
    font-family: monospace;
    font-size: 0.85em;
    background: #f8fafc;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid var(--secondary-color);
}

.modal-content .model-name {
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
}

.token-count {
    font-family: monospace;
    font-weight: 600;
    color: #059669;
}

.cost-value {
    font-family: monospace;
    font-weight: 600;
    color: #dc2626;
    font-size: 1.1em;
}

.error-section {
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 8px;
    padding: 16px;
}

.error-message {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    padding: 12px;
    border-radius: 6px;
    font-family: monospace;
    font-size: 0.9em;
    line-height: 1.4;
    border-left: 4px solid #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 10px;
    }
    
    .detail-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .modal-content {
        max-height: 95vh;
    }
    
    .modal-body {
        padding: 16px;
    }
}
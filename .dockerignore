# Build artifacts
endpoint_forwarder
endpoint_forwarder.exe
endpoint_forwarder_used.exe
*.exe

# Test files
*_test.go

# Documentation
*.md
!README.md

# Configuration files (except example)
*.yaml
!config/example.yaml

# Git
.git
.gitignore

# GitHub workflows
.github

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Logs (will be generated at runtime)
*.log
logs/

# Dependencies (will be downloaded during build)
vendor/

# Docker files (not needed in build context except main ones)
docker-compose.yml
docker-compose.override.yml
docker-compose.*.yml
Dockerfile.dev
.dockerignore

# Development scripts
*.sh
!deploy.sh

# Runtime directories
data/
cache/
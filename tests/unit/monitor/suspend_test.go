package monitor_test

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"cc-forwarder/internal/monitor"
)

// TestMetrics_RecordRequestSuspended tests recording suspended requests
func TestMetrics_RecordRequestSuspended(t *testing.T) {
	m := monitor.NewMetrics()

	// Record initial request to create connection
	connID := m.RecordRequest("test-endpoint", "192.168.1.1", "test-agent", "POST", "/api/test")

	// Test initial state
	if m.SuspendedRequests != 0 {
		t.<PERSON><PERSON><PERSON>("Expected initial SuspendedRequests to be 0, got %d", m.SuspendedRequests)
	}
	if m.TotalSuspendedRequests != 0 {
		t.<PERSON>rrorf("Expected initial TotalSuspendedRequests to be 0, got %d", m.TotalSuspendedRequests)
	}

	// Record suspended request
	m.RecordRequestSuspended(connID)

	// Check metrics after suspension
	if m.SuspendedRequests != 1 {
		t.<PERSON><PERSON><PERSON>("Expected SuspendedRequests to be 1 after recording suspension, got %d", m.SuspendedRequests)
	}
	if m.TotalSuspendedRequests != 1 {
		t.<PERSON><PERSON>rf("Expected TotalSuspendedRequests to be 1 after recording suspension, got %d", m.TotalSuspendedRequests)
	}

	// Check connection status
	conn, exists := m.ActiveConnections[connID]
	if !exists {
		t.Fatalf("Expected connection %s to exist in ActiveConnections", connID)
	}
	if !conn.IsSuspended {
		t.Error("Expected connection to be marked as suspended")
	}
	if conn.Status != "suspended" {
		t.Errorf("Expected connection status to be 'suspended', got '%s'", conn.Status)
	}
	if conn.SuspendedAt.IsZero() {
		t.Error("Expected SuspendedAt to be set")
	}
}

// TestMetrics_RecordRequestResumed tests resuming suspended requests
func TestMetrics_RecordRequestResumed(t *testing.T) {
	m := monitor.NewMetrics()

	// Record initial request and suspend it
	connID := m.RecordRequest("test-endpoint", "192.168.1.1", "test-agent", "POST", "/api/test")
	m.RecordRequestSuspended(connID)

	// Small delay to ensure time difference
	time.Sleep(10 * time.Millisecond)

	// Resume the request
	m.RecordRequestResumed(connID)

	// Check metrics after resumption
	if m.SuspendedRequests != 0 {
		t.Errorf("Expected SuspendedRequests to be 0 after resumption, got %d", m.SuspendedRequests)
	}
	if m.SuccessfulSuspendedRequests != 1 {
		t.Errorf("Expected SuccessfulSuspendedRequests to be 1 after resumption, got %d", m.SuccessfulSuspendedRequests)
	}
	if m.TotalSuspendedRequests != 1 {
		t.Errorf("Expected TotalSuspendedRequests to remain 1 after resumption, got %d", m.TotalSuspendedRequests)
	}

	// Check connection status
	conn, exists := m.ActiveConnections[connID]
	if !exists {
		t.Fatalf("Expected connection %s to exist in ActiveConnections", connID)
	}
	if conn.IsSuspended {
		t.Error("Expected connection to not be marked as suspended after resumption")
	}
	if conn.Status != "active" {
		t.Errorf("Expected connection status to be 'active' after resumption, got '%s'", conn.Status)
	}
	if conn.ResumedAt.IsZero() {
		t.Error("Expected ResumedAt to be set")
	}
	if conn.SuspendedTime == 0 {
		t.Error("Expected SuspendedTime to be greater than 0")
	}

	// Check total suspended time metrics
	if m.TotalSuspendedTime == 0 {
		t.Error("Expected TotalSuspendedTime to be greater than 0")
	}
	if m.MinSuspendedTime == 0 {
		t.Error("Expected MinSuspendedTime to be greater than 0")
	}
	if m.MaxSuspendedTime == 0 {
		t.Error("Expected MaxSuspendedTime to be greater than 0")
	}
}

// TestMetrics_RecordRequestSuspendTimeout tests timing out suspended requests
func TestMetrics_RecordRequestSuspendTimeout(t *testing.T) {
	m := monitor.NewMetrics()

	// Record initial request and suspend it
	connID := m.RecordRequest("test-endpoint", "192.168.1.1", "test-agent", "POST", "/api/test")
	m.RecordRequestSuspended(connID)

	// Small delay to ensure time difference
	time.Sleep(10 * time.Millisecond)

	// Timeout the suspended request
	m.RecordRequestSuspendTimeout(connID)

	// Check metrics after timeout
	if m.SuspendedRequests != 0 {
		t.Errorf("Expected SuspendedRequests to be 0 after timeout, got %d", m.SuspendedRequests)
	}
	if m.TimeoutSuspendedRequests != 1 {
		t.Errorf("Expected TimeoutSuspendedRequests to be 1 after timeout, got %d", m.TimeoutSuspendedRequests)
	}
	if m.TotalSuspendedRequests != 1 {
		t.Errorf("Expected TotalSuspendedRequests to remain 1 after timeout, got %d", m.TotalSuspendedRequests)
	}

	// Check connection moved to history
	_, exists := m.ActiveConnections[connID]
	if exists {
		t.Error("Expected connection to be removed from ActiveConnections after timeout")
	}

	// Check connection in history
	if len(m.ConnectionHistory) == 0 {
		t.Fatal("Expected connection to be added to ConnectionHistory")
	}

	historyConn := m.ConnectionHistory[len(m.ConnectionHistory)-1]
	if historyConn.ID != connID {
		t.Errorf("Expected history connection ID to be %s, got %s", connID, historyConn.ID)
	}
	if historyConn.Status != "timeout" {
		t.Errorf("Expected history connection status to be 'timeout', got '%s'", historyConn.Status)
	}
	if historyConn.SuspendedTime == 0 {
		t.Error("Expected history connection SuspendedTime to be greater than 0")
	}

	// Check total suspended time metrics
	if m.TotalSuspendedTime == 0 {
		t.Error("Expected TotalSuspendedTime to be greater than 0 after timeout")
	}
}

// TestMetrics_GetSuspendedRequestStats tests suspended request statistics
func TestMetrics_GetSuspendedRequestStats(t *testing.T) {
	m := monitor.NewMetrics()

	// Initial stats should be empty
	stats := m.GetSuspendedRequestStats()
	expectedFields := []string{"suspended_requests", "total_suspended_requests", "successful_suspended_requests", "timeout_suspended_requests", "success_rate", "total_suspended_time", "average_suspended_time", "min_suspended_time", "max_suspended_time"}
	for _, field := range expectedFields {
		if _, exists := stats[field]; !exists {
			t.Errorf("Expected stats to contain field '%s'", field)
		}
	}

	// Test with some suspended requests
	connID1 := m.RecordRequest("endpoint1", "192.168.1.1", "agent1", "POST", "/api/1")
	connID2 := m.RecordRequest("endpoint2", "192.168.1.2", "agent2", "GET", "/api/2")
	connID3 := m.RecordRequest("endpoint3", "192.168.1.3", "agent3", "PUT", "/api/3")

	// Suspend all requests
	m.RecordRequestSuspended(connID1)
	m.RecordRequestSuspended(connID2)
	m.RecordRequestSuspended(connID3)

	time.Sleep(20 * time.Millisecond) // Ensure some suspended time

	// Resume one, timeout one, leave one suspended
	m.RecordRequestResumed(connID1)
	m.RecordRequestSuspendTimeout(connID2)
	// connID3 remains suspended

	stats = m.GetSuspendedRequestStats()

	// Check stats values
	if stats["suspended_requests"].(int64) != 1 {
		t.Errorf("Expected suspended_requests to be 1, got %v", stats["suspended_requests"])
	}
	if stats["total_suspended_requests"].(int64) != 3 {
		t.Errorf("Expected total_suspended_requests to be 3, got %v", stats["total_suspended_requests"])
	}
	if stats["successful_suspended_requests"].(int64) != 1 {
		t.Errorf("Expected successful_suspended_requests to be 1, got %v", stats["successful_suspended_requests"])
	}
	if stats["timeout_suspended_requests"].(int64) != 1 {
		t.Errorf("Expected timeout_suspended_requests to be 1, got %v", stats["timeout_suspended_requests"])
	}

	// Check success rate (1 successful out of 2 processed = 50%)
	successRate := stats["success_rate"].(float64)
	if successRate != 50.0 {
		t.Errorf("Expected success_rate to be 50.0, got %f", successRate)
	}
}

// TestMetrics_GetActiveSuspendedConnections tests getting active suspended connections
func TestMetrics_GetActiveSuspendedConnections(t *testing.T) {
	m := monitor.NewMetrics()

	// Initially no suspended connections
	suspendedConns := m.GetActiveSuspendedConnections()
	if len(suspendedConns) != 0 {
		t.Errorf("Expected no suspended connections initially, got %d", len(suspendedConns))
	}

	// Create some connections
	connID1 := m.RecordRequest("endpoint1", "192.168.1.1", "agent1", "POST", "/api/1")
	connID2 := m.RecordRequest("endpoint2", "192.168.1.2", "agent2", "GET", "/api/2")
	connID3 := m.RecordRequest("endpoint3", "192.168.1.3", "agent3", "PUT", "/api/3")

	// Suspend some connections
	m.RecordRequestSuspended(connID1)
	m.RecordRequestSuspended(connID3)
	// connID2 remains active (not suspended)

	// Get suspended connections
	suspendedConns = m.GetActiveSuspendedConnections()
	if len(suspendedConns) != 2 {
		t.Errorf("Expected 2 suspended connections, got %d", len(suspendedConns))
	}

	// Check that the suspended connections are correct
	suspendedIDs := make(map[string]bool)
	for _, conn := range suspendedConns {
		suspendedIDs[conn.ID] = true
		if !conn.IsSuspended {
			t.Errorf("Expected connection %s to be marked as suspended", conn.ID)
		}
		if conn.Status != "suspended" {
			t.Errorf("Expected connection %s status to be 'suspended', got '%s'", conn.ID, conn.Status)
		}
	}

	if !suspendedIDs[connID1] {
		t.Errorf("Expected connection %s to be in suspended connections", connID1)
	}
	if !suspendedIDs[connID3] {
		t.Errorf("Expected connection %s to be in suspended connections", connID3)
	}
	if suspendedIDs[connID2] {
		t.Errorf("Did not expect connection %s to be in suspended connections", connID2)
	}

	// Resume one connection
	m.RecordRequestResumed(connID1)

	// Check suspended connections after resume
	suspendedConns = m.GetActiveSuspendedConnections()
	if len(suspendedConns) != 1 {
		t.Errorf("Expected 1 suspended connection after resume, got %d", len(suspendedConns))
	}
	if suspendedConns[0].ID != connID3 {
		t.Errorf("Expected remaining suspended connection to be %s, got %s", connID3, suspendedConns[0].ID)
	}
}

// TestMetrics_SuspendedRequestHistory tests suspended request history tracking
func TestMetrics_SuspendedRequestHistory(t *testing.T) {
	m := monitor.NewMetrics()

	// Initially no history
	history := m.GetSuspendedRequestHistory()
	if len(history) != 0 {
		t.Errorf("Expected no suspended request history initially, got %d", len(history))
	}

	// Create some actual suspended requests to build history naturally
	for i := 0; i < 5; i++ {
		connID := m.RecordRequest(fmt.Sprintf("endpoint-%d", i), "192.168.1.1", "agent", "POST", "/api")
		m.RecordRequestSuspended(connID)
	}
	
	// Resume some and timeout others
	for i := 0; i < 3; i++ {
		connID := m.RecordRequest(fmt.Sprintf("endpoint-resume-%d", i), "192.168.1.1", "agent", "POST", "/api")
		m.RecordRequestSuspended(connID)
		time.Sleep(1 * time.Millisecond) // Small delay for suspended time
		m.RecordRequestResumed(connID)
	}
	
	for i := 0; i < 2; i++ {
		connID := m.RecordRequest(fmt.Sprintf("endpoint-timeout-%d", i), "192.168.1.1", "agent", "POST", "/api")
		m.RecordRequestSuspended(connID)
		time.Sleep(1 * time.Millisecond) // Small delay for suspended time  
		m.RecordRequestSuspendTimeout(connID)
	}

	// Add history data point using the public API
	m.AddHistoryDataPoints()

	// Check history was created
	history = m.GetSuspendedRequestHistory()
	if len(history) == 0 {
		t.Fatal("Expected suspended request history to be added")
	}

	// Verify the latest history point has reasonable data
	latestPoint := history[len(history)-1]
	if latestPoint.TotalSuspendedRequests == 0 {
		t.Error("Expected TotalSuspendedRequests to be greater than 0")
	}
	if latestPoint.SuccessfulSuspendedRequests == 0 {
		t.Error("Expected SuccessfulSuspendedRequests to be greater than 0")
	}
	if latestPoint.TimeoutSuspendedRequests == 0 {
		t.Error("Expected TimeoutSuspendedRequests to be greater than 0") 
	}
}

// TestMetrics_GetChartDataForSuspendedRequests tests chart data for suspended requests
func TestMetrics_GetChartDataForSuspendedRequests(t *testing.T) {
	m := monitor.NewMetrics()

	// Create some suspended requests and build history naturally
	for i := 0; i < 3; i++ {
		connID := m.RecordRequest(fmt.Sprintf("endpoint-%d", i), "192.168.1.1", "agent", "POST", "/api")
		m.RecordRequestSuspended(connID)
		time.Sleep(1 * time.Millisecond)
		if i < 2 {
			m.RecordRequestResumed(connID)
		} else {
			m.RecordRequestSuspendTimeout(connID)
		}
	}

	// Add history data points
	m.AddHistoryDataPoints()

	// Get chart data for last 15 minutes (should include our data)
	chartData := m.GetChartDataForSuspendedRequests(15)
	if len(chartData) == 0 {
		t.Error("Expected at least some chart data points")
	}

	// Get chart data for last 0 minutes (should be empty since cutoff excludes recent data)
	chartData = m.GetChartDataForSuspendedRequests(0)
	if len(chartData) != 0 {
		t.Errorf("Expected 0 chart data points for 0 minutes, got %d", len(chartData))
	}
}

// TestMetrics_ConcurrentSuspendOperations tests thread safety of suspend operations
func TestMetrics_ConcurrentSuspendOperations(t *testing.T) {
	m := monitor.NewMetrics()

	// Create connections
	numConnections := 100
	connIDs := make([]string, numConnections)
	for i := 0; i < numConnections; i++ {
		connIDs[i] = m.RecordRequest(fmt.Sprintf("endpoint-%d", i), fmt.Sprintf("192.168.1.%d", i%255), fmt.Sprintf("agent-%d", i), "POST", fmt.Sprintf("/api/%d", i))
	}

	var wg sync.WaitGroup

	// Suspend all connections concurrently
	for i := 0; i < numConnections; i++ {
		wg.Add(1)
		go func(connID string) {
			defer wg.Done()
			m.RecordRequestSuspended(connID)
		}(connIDs[i])
	}
	wg.Wait()

	// Check that all are suspended
	if m.SuspendedRequests != int64(numConnections) {
		t.Errorf("Expected SuspendedRequests to be %d, got %d", numConnections, m.SuspendedRequests)
	}
	if m.TotalSuspendedRequests != int64(numConnections) {
		t.Errorf("Expected TotalSuspendedRequests to be %d, got %d", numConnections, m.TotalSuspendedRequests)
	}

	time.Sleep(10 * time.Millisecond) // Ensure some suspended time

	// Resume half, timeout half concurrently
	half := numConnections / 2
	for i := 0; i < half; i++ {
		wg.Add(1)
		go func(connID string) {
			defer wg.Done()
			m.RecordRequestResumed(connID)
		}(connIDs[i])
	}
	for i := half; i < numConnections; i++ {
		wg.Add(1)
		go func(connID string) {
			defer wg.Done()
			m.RecordRequestSuspendTimeout(connID)
		}(connIDs[i])
	}
	wg.Wait()

	// Check final metrics
	if m.SuspendedRequests != 0 {
		t.Errorf("Expected SuspendedRequests to be 0 after all processed, got %d", m.SuspendedRequests)
	}
	if m.SuccessfulSuspendedRequests != int64(half) {
		t.Errorf("Expected SuccessfulSuspendedRequests to be %d, got %d", half, m.SuccessfulSuspendedRequests)
	}
	if m.TimeoutSuspendedRequests != int64(numConnections-half) {
		t.Errorf("Expected TimeoutSuspendedRequests to be %d, got %d", numConnections-half, m.TimeoutSuspendedRequests)
	}

	// Check no active suspended connections
	suspendedConns := m.GetActiveSuspendedConnections()
	if len(suspendedConns) != 0 {
		t.Errorf("Expected no active suspended connections, got %d", len(suspendedConns))
	}
}

// TestMetrics_SuspendedTimeCalculations tests suspended time calculations
func TestMetrics_SuspendedTimeCalculations(t *testing.T) {
	m := monitor.NewMetrics()

	// Test GetAverageSuspendedTime with no data
	avgTime := m.GetAverageSuspendedTime()
	if avgTime != 0 {
		t.Errorf("Expected average suspended time to be 0 with no data, got %v", avgTime)
	}

	// Create and suspend some connections
	connID1 := m.RecordRequest("endpoint1", "192.168.1.1", "agent1", "POST", "/api/1")
	connID2 := m.RecordRequest("endpoint2", "192.168.1.2", "agent2", "GET", "/api/2")

	m.RecordRequestSuspended(connID1)
	m.RecordRequestSuspended(connID2)

	// Wait different amounts of time before resuming/timing out
	time.Sleep(20 * time.Millisecond)
	m.RecordRequestResumed(connID1)

	time.Sleep(30 * time.Millisecond)
	m.RecordRequestSuspendTimeout(connID2)

	// Check suspended time calculations
	avgTime = m.GetAverageSuspendedTime()
	if avgTime == 0 {
		t.Error("Expected average suspended time to be greater than 0")
	}

	if m.MinSuspendedTime == 0 {
		t.Error("Expected MinSuspendedTime to be greater than 0")
	}
	if m.MaxSuspendedTime == 0 {
		t.Error("Expected MaxSuspendedTime to be greater than 0")
	}
	if m.TotalSuspendedTime == 0 {
		t.Error("Expected TotalSuspendedTime to be greater than 0")
	}

	// Max should be >= Min
	if m.MaxSuspendedTime < m.MinSuspendedTime {
		t.Errorf("Expected MaxSuspendedTime (%v) to be >= MinSuspendedTime (%v)", m.MaxSuspendedTime, m.MinSuspendedTime)
	}

	// Average should be reasonable
	expectedAvg := m.TotalSuspendedTime / 2 // 2 processed requests
	if avgTime != expectedAvg {
		t.Errorf("Expected average suspended time to be %v, got %v", expectedAvg, avgTime)
	}
}

// Benchmark tests for suspend operations
func BenchmarkMetrics_RecordRequestSuspended(b *testing.B) {
	m := monitor.NewMetrics()

	// Pre-create connections
	connIDs := make([]string, b.N)
	for i := 0; i < b.N; i++ {
		connIDs[i] = m.RecordRequest(fmt.Sprintf("endpoint-%d", i), "192.168.1.1", "test-agent", "POST", "/api/test")
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		m.RecordRequestSuspended(connIDs[i])
	}
}

func BenchmarkMetrics_RecordRequestResumed(b *testing.B) {
	m := monitor.NewMetrics()

	// Pre-create and suspend connections
	connIDs := make([]string, b.N)
	for i := 0; i < b.N; i++ {
		connIDs[i] = m.RecordRequest(fmt.Sprintf("endpoint-%d", i), "192.168.1.1", "test-agent", "POST", "/api/test")
		m.RecordRequestSuspended(connIDs[i])
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		m.RecordRequestResumed(connIDs[i])
	}
}

func BenchmarkMetrics_GetSuspendedRequestStats(b *testing.B) {
	m := monitor.NewMetrics()

	// Create some test data
	for i := 0; i < 100; i++ {
		connID := m.RecordRequest(fmt.Sprintf("endpoint-%d", i), "192.168.1.1", "test-agent", "POST", "/api/test")
		m.RecordRequestSuspended(connID)
		if i%2 == 0 {
			m.RecordRequestResumed(connID)
		} else {
			m.RecordRequestSuspendTimeout(connID)
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = m.GetSuspendedRequestStats()
	}
}

func BenchmarkMetrics_GetActiveSuspendedConnections(b *testing.B) {
	m := monitor.NewMetrics()

	// Create some suspended connections
	for i := 0; i < 50; i++ {
		connID := m.RecordRequest(fmt.Sprintf("endpoint-%d", i), "192.168.1.1", "test-agent", "POST", "/api/test")
		m.RecordRequestSuspended(connID)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = m.GetActiveSuspendedConnections()
	}
}

// TestMetrics_EdgeCases tests edge cases for suspended requests
func TestMetrics_EdgeCases(t *testing.T) {
	t.Run("Resume non-existent connection", func(t *testing.T) {
		m := monitor.NewMetrics()
		// This should not panic
		m.RecordRequestResumed("non-existent-conn")
	})

	t.Run("Timeout non-existent connection", func(t *testing.T) {
		m := monitor.NewMetrics()
		// This should not panic
		m.RecordRequestSuspendTimeout("non-existent-conn")
	})

	t.Run("Resume non-suspended connection", func(t *testing.T) {
		m := monitor.NewMetrics()
		connID := m.RecordRequest("endpoint", "192.168.1.1", "agent", "POST", "/api")
		// Try to resume without suspending first
		m.RecordRequestResumed(connID)
		// Current implementation increments SuccessfulSuspendedRequests regardless
		// This is the actual behavior, so we test for it
		if m.SuccessfulSuspendedRequests != 1 {
			t.Errorf("Expected SuccessfulSuspendedRequests to be 1, got %d", m.SuccessfulSuspendedRequests)
		}
	})

	t.Run("Double suspend same connection", func(t *testing.T) {
		m := monitor.NewMetrics()
		connID := m.RecordRequest("endpoint", "192.168.1.1", "agent", "POST", "/api")
		m.RecordRequestSuspended(connID)
		initialCount := m.SuspendedRequests
		// Suspend again
		m.RecordRequestSuspended(connID)
		// Count should increase (current implementation doesn't check if already suspended)
		if m.SuspendedRequests != initialCount+1 {
			t.Errorf("Expected SuspendedRequests to be %d, got %d", initialCount+1, m.SuspendedRequests)
		}
	})
}
